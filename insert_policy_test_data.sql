-- Comprehensive test data for Securinest Policy API
-- This script creates tenant, user, services, environments, policies, versions, and bundles
-- Run this after the database migrations have been applied

-- ============================================================================
-- 1. BASIC TENANT AND USER SETUP
-- ============================================================================

-- Insert test user account
INSERT INTO securinest.user_account (
    id,
    kc_sub,
    email,
    display_name,
    created_at,
    updated_at,
    version
) VALUES (
    '********-2222-2222-2222-************',
    'policy-admin-kc-sub-12345',
    '<EMAIL>',
    'Policy Admin',
    NOW(),
    NOW(),
    1
) ON CONFLICT (id) DO NOTHING;

-- Insert test tenant
INSERT INTO securinest.tenant (
    id,
    tenant_id,
    name,
    slug,
    billing_plan,
    region,
    created_at,
    updated_at,
    version
) VALUES (
    '********-1111-1111-1111-********1111',
    '********-1111-1111-1111-********1111',
    'Policy Test Organization',
    'policy-test-org',
    'enterprise',
    'us-east-1',
    NOW(),
    NOW(),
    1
) ON CONFLICT (id) DO NOTHING;

-- Add user as tenant member with OWNER role
INSERT INTO securinest.tenant_member (
    tenant_id,
    user_id,
    role,
    created_at
) VALUES (
    '********-1111-1111-1111-********1111',
    '********-2222-2222-2222-************',
    'OWNER',
    NOW()
) ON CONFLICT (tenant_id, user_id) DO NOTHING;

-- ============================================================================
-- 2. ENVIRONMENTS AND SERVICES
-- ============================================================================

-- Insert test environments
INSERT INTO securinest.environment (
    id,
    tenant_id,
    key,
    name,
    created_at,
    updated_at,
    version
) VALUES 
(
    '44444444-4444-4444-4444-444444444444',
    '********-1111-1111-1111-********1111',
    'prod',
    'Production',
    NOW(),
    NOW(),
    1
),
(
    '55555555-5555-5555-5555-555555555555',
    '********-1111-1111-1111-********1111',
    'staging',
    'Staging',
    NOW(),
    NOW(),
    1
) ON CONFLICT (id) DO NOTHING;

-- Insert test services
INSERT INTO securinest.app_service (
    id,
    tenant_id,
    name,
    slug,
    description,
    created_at,
    updated_at,
    version
) VALUES 
(
    '33333333-3333-3333-3333-333333333333',
    '********-1111-1111-1111-********1111',
    'API Gateway',
    'api-gateway',
    'Main API gateway service for policy enforcement',
    NOW(),
    NOW(),
    1
),
(
    '77777777-7777-7777-7777-777777777777',
    '********-1111-1111-1111-********1111',
    'Payment Service',
    'payment-service',
    'Payment processing microservice',
    NOW(),
    NOW(),
    1
) ON CONFLICT (id) DO NOTHING;

-- Link services to environments
INSERT INTO securinest.service_environment (
    id,
    service_id,
    env_id,
    created_at,
    updated_at,
    version
) VALUES 
(
    '88888888-8888-8888-8888-888888888888',
    '33333333-3333-3333-3333-333333333333',
    '44444444-4444-4444-4444-444444444444',
    NOW(),
    NOW(),
    1
),
(
    '99999999-9999-9999-9999-999999999999',
    '33333333-3333-3333-3333-333333333333',
    '55555555-5555-5555-5555-555555555555',
    NOW(),
    NOW(),
    1
) ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 3. SIGNING KEYS
-- ============================================================================

-- Insert signing keys for the tenant
INSERT INTO securinest.signing_key (
    id,
    tenant_id,
    kid,
    public_key_pem,
    created_at,
    updated_at,
    version,
    revoked_at
) VALUES
(
    'aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa',
    '********-1111-1111-1111-********1111',
    'tenant-key-********',
    '-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA****************1111
QIDAQAB
-----END PUBLIC KEY-----',
    NOW(),
    NOW(),
    1,
    NULL
) ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 4. COMPLIANCE POLICIES
-- ============================================================================

-- Insert test compliance policies
INSERT INTO securinest.compliance_policy (
    id,
    tenant_id,
    name,
    description,
    state,
    created_by,
    created_at,
    updated_at,
    version
) VALUES
(
    'cccccccc-cccc-cccc-cccc-cccccccccccc',
    '********-1111-1111-1111-********1111',
    'API Security Policy',
    'Comprehensive security policy for API endpoints including authentication, authorization, and rate limiting',
    'ACTIVE',
    '********-2222-2222-2222-************',
    NOW() - INTERVAL '7 days',
    NOW() - INTERVAL '1 day',
    2
),
(
    'dddddddd-dddd-dddd-dddd-dddddddddddd',
    '********-1111-1111-1111-********1111',
    'Payment Compliance Policy',
    'PCI DSS compliance policy for payment processing services',
    'DRAFT',
    '********-2222-2222-2222-************',
    NOW() - INTERVAL '3 days',
    NOW() - INTERVAL '3 days',
    1
) ON CONFLICT (id) DO NOTHING;

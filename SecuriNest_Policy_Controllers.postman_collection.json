{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "SecuriNest Policy Controllers", "description": "Complete Postman collection for all policy controllers in SecuriNest backend", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Compliance Policies", "item": [{"name": "Create Policy", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}, {"key": "X-Request-Id", "value": "{{$guid}}", "description": "Optional request tracking ID"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Sample Compliance Policy\",\n  \"description\": \"A sample policy for testing purposes\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies"]}}}, {"name": "List Policies", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies?state=DRAFT&page=0&size=20", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies"], "query": [{"key": "state", "value": "DRAFT", "description": "Optional filter by state (DRAFT, ACTIVE)"}, {"key": "page", "value": "0", "description": "Page number (default: 0)"}, {"key": "size", "value": "20", "description": "Page size (default: 20)"}]}}}, {"name": "Get Policy", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}"]}}}, {"name": "Update Policy", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}, {"key": "If-Match", "value": "{{policyVersion}}", "description": "Required for optimistic locking"}, {"key": "X-Request-Id", "value": "{{$guid}}", "description": "Optional request tracking ID"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Policy Name\",\n  \"description\": \"Updated description\",\n  \"state\": \"ACTIVE\",\n  \"version\": 1\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}"]}}}]}, {"name": "Policy Versions", "item": [{"name": "Create Version", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}, {"key": "X-Request-Id", "value": "{{$guid}}", "description": "Optional request tracking ID"}], "body": {"mode": "raw", "raw": "{\n  \"json\": {\n    \"version\": \"1.0\",\n    \"rules\": [\n      {\n        \"id\": \"rule-1\",\n        \"condition\": \"request.method == 'GET'\",\n        \"action\": \"allow\"\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}/versions", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions"]}}}, {"name": "List Versions", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}/versions?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions"], "query": [{"key": "page", "value": "0", "description": "Page number (default: 0)"}, {"key": "size", "value": "20", "description": "Page size (default: 20)"}]}}}, {"name": "Get Version", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}/versions/{{versionNo}}", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions", "{{versionNo}}"]}}}, {"name": "Update Version Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}, {"key": "X-Request-Id", "value": "{{$guid}}", "description": "Optional request tracking ID"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}/versions/{{versionNo}}", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions", "{{versionNo}}"]}}}]}, {"name": "Policy Bundles", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}], "body": {"mode": "raw", "raw": "{\n  \"policyVersionId\": \"{{policyVersionId}}\",\n  \"serviceId\": \"{{serviceId}}\",\n  \"envId\": \"{{envId}}\",\n  \"enforcementMode\": \"SHADOW\",\n  \"lkgTtlSeconds\": 300,\n  \"sampling\": {\n    \"enabled\": true,\n    \"rate\": 0.1\n  }\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/bundles/compile", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "bundles", "compile"]}}}, {"name": "List Bundles", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/bundles/services/{{serviceId}}/envs/{{envId}}/bundles?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "bundles", "services", "{{serviceId}}", "envs", "{{envId}}", "bundles"], "query": [{"key": "page", "value": "0", "description": "Page number (default: 0)"}, {"key": "size", "value": "20", "description": "Page size (default: 20)"}]}}}, {"name": "Deactivate Bundle", "request": {"method": "POST", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/bundles/services/{{serviceId}}/envs/{{envId}}/bundles/{{bundleId}}/deactivate", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "bundles", "services", "{{serviceId}}", "envs", "{{envId}}", "bundles", "{{bundleId}}", "deactivate"]}}}]}, {"name": "SDK Bundles", "item": [{"name": "Get Current Bundle", "request": {"method": "GET", "header": [{"key": "If-None-Match", "value": "{{etag}}", "description": "Optional ETag for caching"}, {"key": "X-Env-Key", "value": "{{env<PERSON>ey}}", "description": "Environment API key (future use)"}, {"key": "X-Debug-TenantId", "value": "{{tenantId}}", "description": "Debug tenant ID"}, {"key": "X-Debug-ServiceId", "value": "{{serviceId}}", "description": "Debug service ID"}, {"key": "X-Debug-EnvId", "value": "{{envId}}", "description": "Debug environment ID"}], "url": {"raw": "{{baseUrl}}/v1/sdk/bundles/current", "host": ["{{baseUrl}}"], "path": ["v1", "sdk", "bundles", "current"]}}}]}, {"name": "Signing Keys", "item": [{"name": "List Signing Keys", "request": {"method": "GET", "header": [{"key": "X-Debug-UserId", "value": "{{debugUserId}}", "description": "Required for authentication"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/signing-keys", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "signing-keys"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "description": "Base URL for the SecuriNest API"}, {"key": "tenantId", "value": "11111111-1111-1111-1111-111111111111", "description": "Tenant ID for testing"}, {"key": "debugUserId", "value": "22222222-2222-2222-2222-222222222222", "description": "Debug user ID for authentication"}, {"key": "policyId", "value": "", "description": "Policy ID - set this after creating a policy"}, {"key": "versionNo", "value": "1", "description": "Version number"}, {"key": "policyVersion", "value": "1", "description": "Policy version for optimistic locking"}, {"key": "policyVersionId", "value": "", "description": "Policy version ID - set this after creating a version"}, {"key": "serviceId", "value": "33333333-3333-3333-3333-333333333333", "description": "Service ID for testing"}, {"key": "envId", "value": "44444444-4444-4444-4444-444444444444", "description": "Environment ID for testing"}, {"key": "bundleId", "value": "", "description": "Bundle ID - set this after creating a bundle"}, {"key": "etag", "value": "", "description": "ETag for caching - set from response headers"}, {"key": "env<PERSON><PERSON>", "value": "test-env-key", "description": "Environment API key for SDK access"}]}
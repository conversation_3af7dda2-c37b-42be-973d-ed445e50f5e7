-- Policy Versions and Bundles Test Data
-- Run this after insert_policy_test_data.sql

-- ============================================================================
-- 5. COMPLIANCE POLICY VERSIONS
-- ============================================================================

-- Insert policy versions for API Security Policy
INSERT INTO securinest.compliance_policy_version (
    id,
    policy_id,
    version_no,
    author_id,
    json,
    status,
    created_at,
    updated_at,
    version,
    created_at_ts
) VALUES 
-- Version 1 (Draft)
(
    'f0000000-0000-0000-0000-000000000001',
    'cccccccc-cccc-cccc-cccc-cccccccccccc',
    1,
    '22222222-2222-2222-2222-222222222222',
    '{
        "version": "1.0",
        "metadata": {
            "name": "API Security Policy",
            "description": "Basic API security rules",
            "tags": ["security", "api", "authentication"]
        },
        "rules": [
            {
                "id": "auth-required",
                "name": "Authentication Required",
                "description": "All API endpoints must require authentication",
                "enabled": true,
                "severity": "HIGH",
                "conditions": {
                    "endpoints": ["/api/*"],
                    "methods": ["GET", "POST", "PUT", "DELETE"]
                },
                "actions": {
                    "block": false,
                    "log": true,
                    "alert": true
                }
            }
        ]
    }',
    'DRAFT',
    NOW() - INTERVAL '7 days',
    NOW() - INTERVAL '7 days',
    1,
    NOW() - INTERVAL '7 days'
),
-- Version 2 (Active)
(
    'f0000000-0000-0000-0000-000000000002',
    'cccccccc-cccc-cccc-cccc-cccccccccccc',
    2,
    '22222222-2222-2222-2222-222222222222',
    '{
        "version": "1.0",
        "metadata": {
            "name": "API Security Policy",
            "description": "Enhanced API security rules with rate limiting",
            "tags": ["security", "api", "authentication", "rate-limiting"]
        },
        "rules": [
            {
                "id": "auth-required",
                "name": "Authentication Required",
                "description": "All API endpoints must require authentication",
                "enabled": true,
                "severity": "HIGH",
                "conditions": {
                    "endpoints": ["/api/*"],
                    "methods": ["GET", "POST", "PUT", "DELETE"]
                },
                "actions": {
                    "block": true,
                    "log": true,
                    "alert": true
                }
            },
            {
                "id": "rate-limit",
                "name": "Rate Limiting",
                "description": "Enforce rate limits on API endpoints",
                "enabled": true,
                "severity": "MEDIUM",
                "conditions": {
                    "endpoints": ["/api/*"],
                    "methods": ["POST", "PUT", "DELETE"]
                },
                "actions": {
                    "block": true,
                    "log": true,
                    "alert": false
                }
            }
        ]
    }',
    'ACTIVE',
    NOW() - INTERVAL '1 day',
    NOW() - INTERVAL '1 day',
    1,
    NOW() - INTERVAL '1 day'
) ON CONFLICT (id) DO NOTHING;

-- Insert policy version for Payment Compliance Policy
INSERT INTO securinest.compliance_policy_version (
    id,
    policy_id,
    version_no,
    author_id,
    json,
    status,
    created_at,
    updated_at,
    version,
    created_at_ts
) VALUES 
(
    'f0000000-0000-0000-0000-000000000003',
    'dddddddd-dddd-dddd-dddd-dddddddddddd',
    1,
    '22222222-2222-2222-2222-222222222222',
    '{
        "version": "1.0",
        "metadata": {
            "name": "Payment Compliance Policy",
            "description": "PCI DSS compliance rules for payment processing",
            "tags": ["pci-dss", "payment", "compliance", "security"]
        },
        "rules": [
            {
                "id": "pci-encryption",
                "name": "Payment Data Encryption",
                "description": "All payment data must be encrypted in transit and at rest",
                "enabled": true,
                "severity": "CRITICAL",
                "conditions": {
                    "endpoints": ["/api/payments/*", "/api/billing/*"],
                    "methods": ["POST", "PUT"]
                },
                "actions": {
                    "block": true,
                    "log": true,
                    "alert": true
                }
            },
            {
                "id": "pci-logging",
                "name": "Payment Transaction Logging",
                "description": "All payment transactions must be logged for audit purposes",
                "enabled": true,
                "severity": "HIGH",
                "conditions": {
                    "endpoints": ["/api/payments/*"],
                    "methods": ["GET", "POST", "PUT", "DELETE"]
                },
                "actions": {
                    "block": false,
                    "log": true,
                    "alert": false
                }
            }
        ]
    }',
    'DRAFT',
    NOW() - INTERVAL '3 days',
    NOW() - INTERVAL '3 days',
    1,
    NOW() - INTERVAL '3 days'
) ON CONFLICT (id) DO NOTHING;

-- ============================================================================
-- 6. POLICY BUNDLES
-- ============================================================================

-- Insert policy bundles for API Gateway service
INSERT INTO securinest.policy_bundle (
    id,
    tenant_id,
    policy_version_id,
    service_id,
    env_id,
    compiled_json,
    etag,
    signature_b64,
    kid,
    active,
    enforcement_mode,
    lkg_ttl_seconds,
    sampling,
    created_at,
    updated_at,
    version
) VALUES
-- Production bundle (Active)
(
    'b0000000-0000-0000-0000-000000000001',
    '11111111-1111-1111-1111-111111111111',
    'f0000000-0000-0000-0000-000000000002',
    '33333333-3333-3333-3333-333333333333',
    '44444444-4444-4444-4444-444444444444',
    '{"version": "1.0", "compiled_at": "2024-01-15T10:30:00Z", "policy_id": "cccccccc-cccc-cccc-cccc-cccccccccccc", "version_no": 2, "rules": [{"id": "auth-required", "name": "Authentication Required", "enabled": true, "severity": "HIGH", "conditions": {"endpoints": ["/api/*"], "methods": ["GET", "POST", "PUT", "DELETE"]}, "actions": {"block": true, "log": true, "alert": true}}]}',
    'W/"abc123def456"',
    'bW9jay1zaWduYXR1cmUtZm9yLXByb2R1Y3Rpb24tYnVuZGxl',
    'tenant-key-11111111',
    true,
    'ENFORCE',
    86400,
    '{"events": 0.1, "violations": 1.0, "alerts": 0.5}',
    NOW() - INTERVAL '1 day',
    NOW() - INTERVAL '1 day',
    1
) ON CONFLICT (id) DO NOTHING;

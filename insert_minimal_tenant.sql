-- Minimal tenant setup for Securinest Policy API testing
-- Creates just a tenant and user for basic testing

-- Insert a test user account
INSERT INTO securinest.user_account (
    id, 
    kc_sub,
    email, 
    display_name,
    created_at, 
    updated_at, 
    version
) VALUES (
    '********-2222-2222-2222-************',
    'admin-kc-sub-67890',
    '<EMAIL>',
    'Admin User',
    NOW(),
    NOW(),
    1
) ON CONFLICT (id) DO NOTHING;

-- Insert a test tenant
INSERT INTO securinest.tenant (
    id,
    tenant_id,
    name,
    slug,
    billing_plan,
    region,
    created_at,
    updated_at,
    version
) VALUES (
    '********-1111-1111-1111-********1111',
    '********-1111-1111-1111-********1111',
    'Test Organization',
    'test-org',
    'enterprise',
    'us-east-1',
    NOW(),
    NOW(),
    1
) ON CONFLICT (id) DO NOTHING;

-- Add user as tenant member with OWNER role
INSERT INTO securinest.tenant_member (
    tenant_id,
    user_id,
    role,
    created_at
) VALUES (
    '********-1111-1111-1111-********1111',
    '********-2222-2222-2222-************',
    'OWNER',
    NOW()
) ON CONFLICT (tenant_id, user_id) DO NOTHING;

-- Created:
-- Tenant ID: ********-1111-1111-1111-********1111
-- User ID: ********-2222-2222-2222-************
-- User Role: OWNER

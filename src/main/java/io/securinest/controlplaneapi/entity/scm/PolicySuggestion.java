package io.securinest.controlplaneapi.entity.scm;

import io.securinest.controlplaneapi.entity.shared.AbstractTenantEntity;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import java.time.Instant;
import java.util.Map;
import java.util.UUID;

@Entity
@Table(name = "policy_suggestion")
@NoArgsConstructor
@AllArgsConstructor
@Getter
@Setter
public class PolicySuggestion extends AbstractTenantEntity {

    @NotBlank
    @Column(name = "source", nullable = false)
    private String source;

    @Column(name = "service_id")
    private UUID serviceId;

    @Column(name = "repo_id")
    private UUID repoId;

    @NotNull
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "suggestion", nullable = false, columnDefinition = "jsonb")
    private Map<String, Object> suggestion;

    @NotNull
    @Column(name = "confidence", nullable = false)
    private Double confidence;

    @NotNull
    @Column(name = "created_at_ts", nullable = false)
    private Instant createdAtTs;

}

package io.securinest.controlplaneapi.controller.policy;

import io.securinest.controlplaneapi.dto.policy.CompileBundleRequest;
import io.securinest.controlplaneapi.dto.policy.PolicyBundleDto;
import io.securinest.controlplaneapi.service.policy.PolicyBundleService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/tenants/{tenantId}/bundles")
public class PolicyBundlesController {

    private final PolicyBundleService bundleService;

    @PostMapping("compile")
    public ResponseEntity<PolicyBundleDto> compileBundle(
            @PathVariable UUID tenantId,
            @Valid @RequestBody CompileBundleRequest request,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        PolicyBundleDto bundle = bundleService.compileAndSign(tenantId, request, debugUserId);
        return ResponseEntityUtils.created(bundle);
    }

    @GetMapping("/services/{serviceId}/envs/{envId}/bundles")
    public ResponseEntity<Page<PolicyBundleDto>> listBundles(
            @PathVariable UUID tenantId,
            @PathVariable UUID serviceId,
            @PathVariable UUID envId,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        Pageable pageable = PageRequest.of(page, size);
        Page<PolicyBundleDto> bundles = bundleService.list(tenantId, serviceId, envId, pageable);

        return ResponseEntityUtils.ok(bundles);
    }

    @PostMapping("/services/{serviceId}/envs/{envId}/bundles/{bundleId}/deactivate")
    public ResponseEntity<Void> deactivateBundle(
            @PathVariable UUID tenantId,
            @PathVariable UUID serviceId,
            @PathVariable UUID envId,
            @PathVariable UUID bundleId,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        bundleService.deactivate(tenantId, bundleId, envId, serviceId);
        return ResponseEntity.noContent().build();
    }
}

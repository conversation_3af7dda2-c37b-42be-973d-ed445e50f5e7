package io.securinest.controlplaneapi.controller.policy;

import io.securinest.controlplaneapi.dto.policy.SigningKeyDto;
import io.securinest.controlplaneapi.service.policy.SigningKeyService;
import io.securinest.controlplaneapi.util.shared.ResponseEntityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.UUID;

@RestController
@RequiredArgsConstructor
@RequestMapping("/v1/tenants/{tenantId}/signing-keys")
public class SigningKeysController {

    private final SigningKeyService signingKeyService;

    @GetMapping
    public ResponseEntity<List<SigningKeyDto>> listSigningKeys(
            @PathVariable UUID tenantId,
            @RequestHeader(name = "X-Debug-UserId", required = false) UUID debugUserId) {

        if (debugUserId == null) {
            return ResponseEntity.status(HttpStatus.UNAUTHORIZED).build();
        }

        List<SigningKeyDto> keys = signingKeyService.listPublicKeys(tenantId);
        return ResponseEntityUtils.ok(keys);
    }
}

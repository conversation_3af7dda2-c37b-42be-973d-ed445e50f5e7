package io.securinest.controlplaneapi.util.tenant;

import io.securinest.controlplaneapi.entity.tenant.Tenant;
import io.securinest.controlplaneapi.projection.tenant.TenantChangeSet;
import io.securinest.controlplaneapi.projection.tenant.TenantPatch;

import java.util.ArrayList;
import java.util.List;

public class TenantUtils {

    public static TenantChangeSet computeChangeSet(Tenant currentTenant, TenantPatch patch) {
        List<String> changedFields = new ArrayList<>();
        String newName = null;
        String newSlug = null;
        String newRegion = null;
        String newBillingPlan = null;

        if (patch.name() != null && !patch.name().equals(currentTenant.getName())) {
            newName = patch.name();
            changedFields.add("name");
        }
        if (patch.slug() != null && !patch.slug().equals(currentTenant.getSlug())) {
            newSlug = patch.slug();
            changedFields.add("slug");
        }
        if (patch.region() != null && !patch.region().equals(currentTenant.getRegion())) {
            newRegion = patch.region();
            changedFields.add("region");
        }
        if (patch.billingPlan() != null && !patch.billingPlan().equals(currentTenant.getBillingPlan())) {
            newBillingPlan = patch.billingPlan();
            changedFields.add("billingPlan");
        }

        return new TenantChangeSet(
                !changedFields.isEmpty(),
                List.copyOf(changedFields),
                newName,
                newSlug,
                newRegion,
                newBillingPlan
        );
    }

    public static void applyChangeSet(Tenant tenant, TenantChangeSet changeSet) {
        if (changeSet.name() != null) {
            tenant.setName(changeSet.name());
        }
        if (changeSet.slug() != null) {
            tenant.setSlug(changeSet.slug());
        }
        if (changeSet.region() != null) {
            tenant.setRegion(changeSet.region());
        }
        if (changeSet.billingPlan() != null) {
            tenant.setBillingPlan(changeSet.billingPlan());
        }
    }

    public static Tenant createFromPatch(TenantPatch patch) {
        Tenant tenant = new Tenant();
        if (patch.name() != null) tenant.setName(patch.name());
        if (patch.slug() != null) tenant.setSlug(patch.slug());
        if (patch.region() != null) tenant.setRegion(patch.region());
        if (patch.billingPlan() != null) tenant.setBillingPlan(patch.billingPlan());
        return tenant;
    }

    public static Integer parseIfMatch(String ifMatch) {
        if (ifMatch == null) return null;
        for (String token : ifMatch.split(",")) {
            String value = token.trim();
            if (value.startsWith("W/")) value = value.substring(2).trim();
            if (value.startsWith("\"") && value.endsWith("\"")) value = value.substring(1, value.length()-1);
            try { return Integer.parseInt(value); } catch (NumberFormatException ignored) {}
        }
        return null;
    }


}

package io.securinest.controlplaneapi.util.shared;

import io.securinest.controlplaneapi.exceptions.shared.SecurinestException;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpStatus;

import java.util.Locale;
@UtilityClass
public class ValidationUtils {

    public static final int NAME_MIN_LENGTH = 3;
    public static final int NAME_MAX_LENGTH = 200;

    public static void validateName(String nameValue, String fieldName) {
        if (nameValue == null || nameValue.length() < NAME_MIN_LENGTH || nameValue.length() > NAME_MAX_LENGTH) {
            throw new SecurinestException(HttpStatus.BAD_REQUEST,
                fieldName + " must be " + NAME_MIN_LENGTH + "-" + NAME_MAX_LENGTH + " characters");
        }
    }


    public static String normalizeSlug(String rawSlug) {
        String trimmedSlug = trimToNull(rawSlug);
        if (trimmedSlug == null) return null;
        return trimmedSlug.toLowerCase(Locale.ROOT)
                .replaceAll("[\\s_]+", "-")
                .replaceAll("^-+|-+$", "");
    }

    public static String normalizeName(String rawName) {
        return trimToNull(rawName);
    }

    public static String trimToNull(String inputValue) {
        if (inputValue == null) return null;
        String trimmedValue = inputValue.trim();
        return trimmedValue.isEmpty() ? null : trimmedValue;
    }

    public static int defaultInt(Integer inputValue, int defaultValue) {
        return inputValue == null ? defaultValue : inputValue;
    }

    public static long defaultLong(Long inputValue, long defaultValue) {
        return inputValue == null ? defaultValue : inputValue;
    }
}

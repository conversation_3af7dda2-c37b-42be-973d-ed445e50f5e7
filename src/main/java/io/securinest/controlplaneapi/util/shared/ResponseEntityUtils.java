package io.securinest.controlplaneapi.util.shared;

import io.securinest.controlplaneapi.dto.shared.VersionedResource;
import lombok.experimental.UtilityClass;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import java.net.URI;

@UtilityClass
public class ResponseEntityUtils {

    public static <T extends VersionedResource> ResponseEntity<T> updateResponse(T res) {
        return ResponseEntity.ok()
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .eTag(weakEtag(res.version()))
                .body(res);
    }

    public static <T extends VersionedResource> ResponseEntity<T> createResponse(T res) {
        URI location = ServletUriComponentsBuilder.fromCurrentRequest()
                .path("/{id}")
                .buildAndExpand(res.id())
                .toUri();

        return ResponseEntity.created(location)
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .eTag(weakEtag(res.version()))
                .body(res);
    }

    public static <T> ResponseEntity<T> ok(T body) {
        return ResponseEntity.ok()
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .body(body);
    }

    public static <T> ResponseEntity<T> created(T body) {
        return ResponseEntity.status(201)
                .header(HttpHeaders.CACHE_CONTROL, "no-store")
                .body(body);
    }

    private static String weakEtag(Integer version) {
        return "W/\"" + version + "\"";
    }
}


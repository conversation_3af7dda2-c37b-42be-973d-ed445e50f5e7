package io.securinest.controlplaneapi.service.policy;

import io.securinest.controlplaneapi.dto.policy.SdkBundleResponse;
import io.securinest.controlplaneapi.entity.policy.PolicyBundle;
import io.securinest.controlplaneapi.mapper.policy.PolicyBundleMapper;
import io.securinest.controlplaneapi.repository.policy.PolicyBundleRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class SdkBundleService {

    private final PolicyBundleRepository bundleRepository;
    private final PolicyBundleMapper bundleMapper;

    @Transactional(readOnly = true)
    public Optional<SdkBundleResponse> getCurrent(UUID tenantId, UUID serviceId, UUID envId, String ifNoneMatch) {
        log.debug("Getting current bundle for tenant: {}, service: {}, env: {}", tenantId, serviceId, envId);
        
        Optional<PolicyBundle> bundleOpt = bundleRepository
                .findTopByTenantIdAndServiceIdAndEnvIdAndActiveTrueOrderByCreatedAtDesc(tenantId, serviceId, envId);
        
        if (bundleOpt.isEmpty()) {
            log.debug("No active bundle found for tenant: {}, service: {}, env: {}", tenantId, serviceId, envId);
            return Optional.empty();
        }
        
        PolicyBundle bundle = bundleOpt.get();
        
        // Check ETag for 304 Not Modified
        if (ifNoneMatch != null && ifNoneMatch.equals(bundle.getEtag())) {
            log.debug("Bundle ETag matches If-None-Match header, returning empty for 304");
            return Optional.empty();
        }
        
        SdkBundleResponse response = bundleMapper.toSdkResponse(bundle);
        log.debug("Returning current bundle: {} for tenant: {}, service: {}, env: {}", 
                 bundle.getId(), tenantId, serviceId, envId);
        
        return Optional.of(response);
    }
    
    @Transactional(readOnly = true)
    public String getCurrentETag(UUID tenantId, UUID serviceId, UUID envId) {
        return bundleRepository
                .findTopByTenantIdAndServiceIdAndEnvIdAndActiveTrueOrderByCreatedAtDesc(tenantId, serviceId, envId)
                .map(PolicyBundle::getEtag)
                .orElse(null);
    }
}

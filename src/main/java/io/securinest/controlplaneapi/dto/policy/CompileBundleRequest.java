package io.securinest.controlplaneapi.dto.policy;

import jakarta.validation.constraints.NotNull;
import lombok.Builder;

import java.util.Map;

@Builder
public record CompileBundleRequest(
        @NotNull
        String policyVersionId,
        
        @NotNull
        String serviceId,
        
        @NotNull
        String envId,
        
        @NotNull
        String enforcementMode,
        
        Integer lkgTtlSeconds,
        
        Map<String, Object> sampling
) {
}

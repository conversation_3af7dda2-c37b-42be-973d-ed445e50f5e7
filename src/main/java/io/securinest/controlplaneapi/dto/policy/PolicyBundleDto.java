package io.securinest.controlplaneapi.dto.policy;

import lombok.Builder;

import java.time.Instant;
import java.util.Map;

@Builder
public record PolicyBundleDto(
        String id,
        String tenantId,
        String policyVersionId,
        String serviceId,
        String envId,
        String etag,
        String kid,
        boolean active,
        String enforcementMode,
        Integer lkgTtlSeconds,
        Map<String, Object> sampling,
        Instant createdAt,
        Instant updatedAt,
        Long version
) {
}

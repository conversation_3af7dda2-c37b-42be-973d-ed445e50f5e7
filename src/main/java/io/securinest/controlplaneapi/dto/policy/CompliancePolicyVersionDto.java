package io.securinest.controlplaneapi.dto.policy;

import lombok.Builder;

import java.time.Instant;
import java.util.Map;

@Builder
public record CompliancePolicyVersionDto(
        String id,
        String policyId,
        Integer versionNo,
        String authorId,
        Map<String, Object> json,
        String status,
        Instant createdAt,
        Instant updatedAt,
        Long version,
        Instant createdAtTs
) {
}

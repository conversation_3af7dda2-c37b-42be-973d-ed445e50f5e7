# Securinest Policy API Test Data Summary

This document provides a complete overview of the test data created for the Securinest Policy API.

## 🚀 How to Run the Scripts

Execute these SQL scripts in order after running the database migrations:

```bash
# 1. Run database migrations first
mvn spring-boot:run  # This will run Flyway migrations automatically

# 2. Then run the test data scripts
psql -h localhost -U postgres -d securinest -f insert_policy_test_data.sql
psql -h localhost -U postgres -d securinest -f insert_policy_versions_and_bundles.sql
```

## 📋 Test Data Overview

### **Tenant & User**
- **Tenant ID**: `********-1111-1111-1111-************`
- **Tenant Name**: Policy Test Organization
- **User ID**: `22222222-2222-2222-2222-222222222222`
- **User Email**: <EMAIL>
- **User Role**: OWNER

### **Services**
- **API Gateway**: `33333333-3333-3333-3333-333333333333`
- **Payment Service**: `77777777-7777-7777-7777-777777777777`

### **Environments**
- **Production**: `44444444-4444-4444-4444-444444444444`
- **Staging**: `55555555-5555-5555-5555-555555555555`

### **Signing Keys**
- **Active Key**: `aaaaaaaa-aaaa-aaaa-aaaa-aaaaaaaaaaaa`
  - KID: `tenant-key-********`
  - Status: Active
- **Revoked Key**: `bbbbbbbb-bbbb-bbbb-bbbb-bbbbbbbbbbbb`
  - KID: `tenant-key-backup-********`
  - Status: Revoked (for testing)

### **Compliance Policies**

#### 1. API Security Policy
- **Policy ID**: `cccccccc-cccc-cccc-cccc-cccccccccccc`
- **Name**: API Security Policy
- **State**: ACTIVE
- **Description**: Comprehensive security policy for API endpoints
- **Versions**: 2 (v1: DRAFT, v2: ACTIVE)

#### 2. Payment Compliance Policy
- **Policy ID**: `dddddddd-dddd-dddd-dddd-dddddddddddd`
- **Name**: Payment Compliance Policy
- **State**: DRAFT
- **Description**: PCI DSS compliance policy for payment processing
- **Versions**: 1 (v1: DRAFT)

### **Policy Versions**

#### API Security Policy Versions
- **Version 1**: `f0000000-0000-0000-0000-000000000001`
  - Status: DRAFT
  - Rules: Basic authentication requirement
  
- **Version 2**: `f0000000-0000-0000-0000-000000000002`
  - Status: ACTIVE
  - Rules: Authentication + Rate limiting

#### Payment Policy Version
- **Version 1**: `f0000000-0000-0000-0000-000000000003`
  - Status: DRAFT
  - Rules: PCI DSS encryption and logging

### **Policy Bundles**

#### Production Bundle
- **Bundle ID**: `b0000000-0000-0000-0000-000000000001`
- **Service**: API Gateway
- **Environment**: Production
- **Policy Version**: API Security Policy v2
- **Enforcement Mode**: ENFORCE
- **Status**: Active
- **ETag**: `W/"abc123def456"`

#### Staging Bundle
- **Bundle ID**: `b0000000-0000-0000-0000-000000000002`
- **Service**: API Gateway
- **Environment**: Staging
- **Policy Version**: API Security Policy v2
- **Enforcement Mode**: SHADOW
- **Status**: Active
- **ETag**: `W/"xyz789uvw012"`

## 🔧 Postman Collection Variables

Update your Postman collection with these variables:

```json
{
  "baseUrl": "http://localhost:8080",
  "tenantId": "********-1111-1111-1111-************",
  "userId": "22222222-2222-2222-2222-222222222222",
  "serviceId": "33333333-3333-3333-3333-333333333333",
  "envId": "44444444-4444-4444-4444-444444444444",
  "policyId": "cccccccc-cccc-cccc-cccc-cccccccccccc",
  "policyVersionId": "f0000000-0000-0000-0000-000000000002",
  "bundleId": "b0000000-0000-0000-0000-000000000001",
  "versionNo": "2",
  "etag": "W/\"abc123def456\""
}
```

## 🧪 Test Scenarios Enabled

With this test data, you can test:

1. **Policy Management**:
   - List policies (both ACTIVE and DRAFT)
   - Get specific policies
   - Update policy states
   - Create new policies

2. **Version Management**:
   - List policy versions
   - Get specific versions
   - Create new versions
   - Update version status (DRAFT → ACTIVE)

3. **Bundle Management**:
   - Compile and sign bundles
   - List bundles for service/environment
   - Deactivate bundles

4. **SDK Integration**:
   - Get current bundle with ETag support
   - Test 304 Not Modified responses
   - Test different enforcement modes

5. **Signing Keys**:
   - List public keys
   - Test with active and revoked keys

## 🔍 Verification Queries

After running the scripts, verify the data:

```sql
-- Check tenant and user
SELECT t.name, tm.role, u.email 
FROM securinest.tenant t
JOIN securinest.tenant_member tm ON t.id = tm.tenant_id
JOIN securinest.user_account u ON tm.user_id = u.id
WHERE t.id = '********-1111-1111-1111-************';

-- Check policies and versions
SELECT p.name, p.state, COUNT(pv.id) as version_count
FROM securinest.compliance_policy p
LEFT JOIN securinest.compliance_policy_version pv ON p.id = pv.policy_id
WHERE p.tenant_id = '********-1111-1111-1111-************'
GROUP BY p.id, p.name, p.state;

-- Check active bundles
SELECT pb.id, s.name as service, e.name as environment, pb.enforcement_mode, pb.active
FROM securinest.policy_bundle pb
JOIN securinest.app_service s ON pb.service_id = s.id
JOIN securinest.environment e ON pb.env_id = e.id
WHERE pb.tenant_id = '********-1111-1111-1111-************'
AND pb.active = true;

-- Check signing keys
SELECT kid, revoked_at IS NULL as active
FROM securinest.signing_key
WHERE tenant_id = '********-1111-1111-1111-************';
```

## 🎯 Ready for Testing!

Your database now contains comprehensive test data that covers all aspects of the Policy API. You can start testing all endpoints with realistic data scenarios! 🚀

{"info": {"_postman_id": "12345678-1234-1234-1234-123456789012", "name": "Securinest Policy API - Localhost", "description": "Complete API collection for testing Securinest Policy Management endpoints on localhost", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Compliance Policies", "item": [{"name": "Create Policy", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{userId}}", "description": "Debug user ID for development"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Sample Security Policy\",\n  \"description\": \"A sample policy for testing API endpoints\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies"]}}}, {"name": "List Policies", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}, {"key": "state", "value": "DRAFT", "disabled": true}]}}}, {"name": "Get Policy by ID", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}"]}}}, {"name": "Update Policy", "request": {"method": "PUT", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{userId}}"}, {"key": "If-Match", "value": "{{policyVersion}}", "description": "Policy version for optimistic locking"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"Updated Security Policy\",\n  \"description\": \"Updated description for the policy\",\n  \"state\": \"ACTIVE\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}"]}}}]}, {"name": "Policy Versions", "item": [{"name": "Create Policy Version", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{userId}}"}], "body": {"mode": "raw", "raw": "{\n  \"json\": {\n    \"version\": \"1.0\",\n    \"metadata\": {\n      \"name\": \"Sample Policy\",\n      \"description\": \"A sample compliance policy\",\n      \"tags\": [\"security\", \"compliance\"]\n    },\n    \"rules\": [\n      {\n        \"id\": \"rule-001\",\n        \"name\": \"Authentication Required\",\n        \"description\": \"All endpoints must require authentication\",\n        \"enabled\": true,\n        \"severity\": \"HIGH\",\n        \"conditions\": {\n          \"endpoints\": [\"/api/*\"],\n          \"methods\": [\"GET\", \"POST\", \"PUT\", \"DELETE\"]\n        },\n        \"actions\": {\n          \"block\": false,\n          \"log\": true,\n          \"alert\": true\n        }\n      }\n    ]\n  }\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}/versions", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions"]}}}, {"name": "List Policy Versions", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}/versions?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}]}}}, {"name": "Get Policy Version", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}/versions/{{versionNo}}", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions", "{{versionNo}}"]}}}, {"name": "Update Version Status", "request": {"method": "PATCH", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{userId}}"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"ACTIVE\"\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/policies/{{policyId}}/versions/{{versionNo}}", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "policies", "{{policyId}}", "versions", "{{versionNo}}"]}}}]}, {"name": "Policy Bundles", "item": [{"name": "<PERSON><PERSON><PERSON>", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}, {"key": "X-Debug-UserId", "value": "{{userId}}"}], "body": {"mode": "raw", "raw": "{\n  \"policyVersionId\": \"{{policyVersionId}}\",\n  \"serviceId\": \"{{serviceId}}\",\n  \"envId\": \"{{envId}}\",\n  \"enforcementMode\": \"SHADOW\",\n  \"lkgTtlSeconds\": 86400,\n  \"sampling\": {\n    \"events\": 0.1,\n    \"violations\": 1.0\n  }\n}"}, "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/bundles/compile", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "bundles", "compile"]}}}, {"name": "List Bundles", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/services/{{serviceId}}/envs/{{envId}}/bundles?page=0&size=20", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "services", "{{serviceId}}", "envs", "{{envId}}", "bundles"], "query": [{"key": "page", "value": "0"}, {"key": "size", "value": "20"}]}}}, {"name": "Deactivate Bundle", "request": {"method": "POST", "header": [{"key": "X-Debug-UserId", "value": "{{userId}}"}], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/services/{{serviceId}}/envs/{{envId}}/bundles/{{bundleId}}/deactivate", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "services", "{{serviceId}}", "envs", "{{envId}}", "bundles", "{{bundleId}}", "deactivate"]}}}]}, {"name": "SDK Bundles", "item": [{"name": "Get Current Bundle", "request": {"method": "GET", "header": [{"key": "X-Debug-TenantId", "value": "{{tenantId}}"}, {"key": "X-Debug-ServiceId", "value": "{{serviceId}}"}, {"key": "X-Debug-EnvId", "value": "{{envId}}"}, {"key": "If-None-Match", "value": "{{etag}}", "disabled": true, "description": "ETag for caching - enable to test 304 responses"}], "url": {"raw": "{{baseUrl}}/v1/sdk/bundles/current", "host": ["{{baseUrl}}"], "path": ["v1", "sdk", "bundles", "current"]}}}]}, {"name": "Signing Keys", "item": [{"name": "List Signing Keys", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/v1/tenants/{{tenantId}}/signing-keys", "host": ["{{baseUrl}}"], "path": ["v1", "tenants", "{{tenantId}}", "signing-keys"]}}}]}], "variable": [{"key": "baseUrl", "value": "http://localhost:8080", "type": "string"}, {"key": "tenantId", "value": "11111111-1111-1111-1111-111111111111", "type": "string"}, {"key": "userId", "value": "22222222-2222-2222-2222-222222222222", "type": "string"}, {"key": "policyId", "value": "", "type": "string"}, {"key": "policyVersion", "value": "1", "type": "string"}, {"key": "versionNo", "value": "1", "type": "string"}]}
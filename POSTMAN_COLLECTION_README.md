# SecuriNest Policy Controllers - Postman Collection

This Postman collection provides comprehensive API testing for all policy controllers in the SecuriNest backend application.

## Collection Overview

The collection includes requests for the following controllers:

### 1. Compliance Policies Controller (`/v1/tenants/{tenantId}/policies`)
- **Create Policy** - POST: Create a new compliance policy
- **List Policies** - GET: List policies with optional state filtering and pagination
- **Get Policy** - GET: Retrieve a specific policy by ID
- **Update Policy** - PUT: Update policy with optimistic locking

### 2. Policy Versions Controller (`/v1/tenants/{tenantId}/policies/{policyId}/versions`)
- **Create Version** - POST: Create a new policy version with JSON content
- **List Versions** - GET: List all versions for a policy with pagination
- **Get Version** - GET: Retrieve a specific version by number
- **Update Version Status** - PATCH: Update version status (DRAFT/ACTIVE)

### 3. Policy Bundles Controller (`/v1/tenants/{tenantId}/bundles`)
- **Compile Bundle** - POST: Compile and sign a policy bundle for deployment
- **List Bundles** - GET: List bundles for a specific service and environment
- **Deactivate Bundle** - POST: Deactivate a specific bundle

### 4. SDK Bundles Controller (`/v1/sdk/bundles`)
- **Get Current Bundle** - GET: Retrieve current bundle with ETag support for caching

### 5. Signing Keys Controller (`/v1/tenants/{tenantId}/signing-keys`)
- **List Signing Keys** - GET: List public signing keys for a tenant

## Setup Instructions

### 1. Import the Collection
1. Open Postman
2. Click "Import" button
3. Select the `SecuriNest_Policy_Controllers.postman_collection.json` file
4. Click "Import"

### 2. Configure Environment Variables
The collection uses the following variables that you should configure:

#### Required Variables:
- `baseUrl`: API base URL (default: `http://localhost:8080`)
- `tenantId`: Tenant ID for testing (default: `11111111-1111-1111-1111-111111111111`)
- `debugUserId`: Debug user ID for authentication (default: `22222222-2222-2222-2222-222222222222`)
- `serviceId`: Service ID for testing (default: `33333333-3333-3333-3333-333333333333`)
- `envId`: Environment ID for testing (default: `44444444-4444-4444-4444-444444444444`)

#### Variables Set During Testing:
- `policyId`: Set after creating a policy
- `versionNo`: Version number (default: `1`)
- `policyVersion`: Policy version for optimistic locking
- `policyVersionId`: Set after creating a policy version
- `bundleId`: Set after creating a bundle
- `etag`: ETag for caching
- `envKey`: Environment API key

### 3. Authentication
All endpoints require the `X-Debug-UserId` header for authentication. This is automatically included in requests using the `{{debugUserId}}` variable.

## Usage Workflow

### Basic Policy Management Workflow:

1. **Create a Policy**
   - Use "Create Policy" request
   - Copy the returned `id` to the `policyId` variable

2. **Create a Policy Version**
   - Use "Create Version" request
   - Copy the returned `id` to the `policyVersionId` variable

3. **Activate the Version**
   - Use "Update Version Status" request
   - Set status to "ACTIVE"

4. **Compile a Bundle**
   - Use "Compile Bundle" request
   - Copy the returned `id` to the `bundleId` variable

5. **Test SDK Access**
   - Use "Get Current Bundle" request to verify deployment

## Request Details

### Authentication Headers
All requests include:
- `X-Debug-UserId`: Required for authentication
- `X-Request-Id`: Optional request tracking (auto-generated UUID)

### Content Types
- POST/PUT/PATCH requests use `Content-Type: application/json`
- GET requests don't require content type headers

### Optimistic Locking
The "Update Policy" request requires:
- `If-Match` header with the current policy version
- `version` field in the request body

### Pagination
List endpoints support pagination:
- `page`: Page number (default: 0)
- `size`: Page size (default: 20)

### State Filtering
Policy list endpoint supports state filtering:
- `state`: DRAFT, ACTIVE (optional)

## Sample Request Bodies

### Create Policy Request:
```json
{
  "name": "Sample Compliance Policy",
  "description": "A sample policy for testing purposes"
}
```

### Create Version Request:
```json
{
  "json": {
    "version": "1.0",
    "rules": [
      {
        "id": "rule-1",
        "condition": "request.method == 'GET'",
        "action": "allow"
      }
    ]
  }
}
```

### Compile Bundle Request:
```json
{
  "policyVersionId": "{{policyVersionId}}",
  "serviceId": "{{serviceId}}",
  "envId": "{{envId}}",
  "enforcementMode": "SHADOW",
  "lkgTtlSeconds": 300,
  "sampling": {
    "enabled": true,
    "rate": 0.1
  }
}
```

## Response Examples

### Policy Response:
```json
{
  "id": "uuid",
  "tenantId": "uuid",
  "name": "Policy Name",
  "description": "Policy Description",
  "state": "DRAFT",
  "createdBy": "uuid",
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "version": 1
}
```

### Bundle Response:
```json
{
  "id": "uuid",
  "tenantId": "uuid",
  "policyVersionId": "uuid",
  "serviceId": "uuid",
  "envId": "uuid",
  "etag": "etag-value",
  "kid": "key-id",
  "active": true,
  "enforcementMode": "SHADOW",
  "lkgTtlSeconds": 300,
  "sampling": {...},
  "createdAt": "2024-01-01T00:00:00Z",
  "updatedAt": "2024-01-01T00:00:00Z",
  "version": 1
}
```

## Error Handling

Common HTTP status codes:
- `200 OK`: Successful GET requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE/deactivate requests
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Missing or invalid authentication
- `404 Not Found`: Resource not found
- `409 Conflict`: Optimistic locking conflict
- `412 Precondition Failed`: Missing If-Match header

## Testing Tips

1. **Use the Collection Runner** for automated testing of the complete workflow
2. **Set up Pre-request Scripts** to automatically extract IDs from responses
3. **Use Tests tab** to validate response status codes and data
4. **Monitor Console** for debugging request/response details
5. **Export Environment** to share configurations with team members

## Development Notes

- The collection is designed for the development environment with debug headers
- Production usage would require proper API key authentication
- ETag support is implemented for efficient caching in SDK endpoints
- All timestamps are in ISO 8601 format with UTC timezone
